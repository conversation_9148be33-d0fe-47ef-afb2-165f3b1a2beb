"""
Market Type Scanner for Multi-Market Type Scanner.
Handles scanning of different market types (EQUITY, INDEX, FUTURES, OPTIONS).
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from config_loader import ConfigLoader
from universal_symbol_parser import UniversalSymbolParser, UniversalSymbol
from options_chain_filter import OptionsChainFilter
from fyers_client import FyersClient
from technical_indicators import MAEAnalyzer

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_percent: float = 0.0

@dataclass
class FilteredSymbol:
    """Filtered symbol with market data and metadata."""
    symbol: str
    underlying: str
    market_type: str
    market_data: MarketData
    
    # Market type specific fields
    suffix: Optional[str] = None
    expiry_year: Optional[str] = None
    expiry_month: Optional[str] = None
    strike_price: Optional[float] = None
    option_type: Optional[str] = None


class BaseMarketScanner(ABC):
    """Base class for market type scanners."""
    
    def __init__(self, config: ConfigLoader, market_type: str):
        """
        Initialize the base scanner.
        
        Args:
            config: Configuration loader instance
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
        """
        self.config = config
        self.market_type = market_type
        self.fyers_client = None
        
        # Initialize symbol parser
        self.symbol_parser = UniversalSymbolParser(config, config.symbols)
        
        logger.info(f"Initialized {market_type} scanner")
    
    def authenticate_fyers(self) -> bool:
        """Authenticate with Fyers API."""
        try:
            self.fyers_client = FyersClient(self.config.env_path)
            return self.fyers_client.authenticate()
        except Exception as e:
            logger.error(f"Failed to authenticate with Fyers: {e}")
            return False
    
    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning for this market type."""
        return self.symbol_parser.get_symbols_for_market_type(
            self.market_type, underlying_symbols
        )
    
    def fetch_market_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """Fetch market data for symbols with performance optimization."""
        if not self.fyers_client:
            logger.error("Fyers client not authenticated")
            return {}

        try:
            # Performance optimization: Use optimized method for large symbol lists
            if len(symbols) > 5000:
                logger.info(f"Using optimized fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes_optimized(symbols, chunk_size=2000)
            else:
                market_data = self.fyers_client.get_quotes(symbols)

            # Convert to MarketData objects (from fyers_client.MarketData to market_type_scanner.MarketData)
            converted_data = {}
            for symbol, fyers_data in market_data.items():
                converted_data[symbol] = MarketData(
                    symbol=symbol,
                    ltp=fyers_data.ltp,
                    volume=fyers_data.volume,
                    open_price=fyers_data.open_price,
                    high_price=fyers_data.high,
                    low_price=fyers_data.low,
                    close_price=fyers_data.close,
                    prev_close=fyers_data.prev_close,
                    change=fyers_data.change,
                    change_percent=fyers_data.change_percent
                )

            return converted_data
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}
    
    def apply_volume_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply volume filter. For INDEX market type, volume filtering is disabled."""
        # Skip volume filtering for INDEX market type
        if self.market_type == 'INDEX':
            logger.info(f"Volume filter skipped for {self.market_type} market type")
            return market_data

        filtered_data = {}

        for symbol, data in market_data.items():
            if self.config.min_volume <= data.volume <= self.config.max_volume:
                filtered_data[symbol] = data

        logger.info(f"Volume filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def apply_ltp_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply LTP filter."""
        filtered_data = {}
        
        for symbol, data in market_data.items():
            if self.config.min_ltp_price <= data.ltp <= self.config.max_ltp_price:
                filtered_data[symbol] = data
        
        logger.info(f"LTP filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    @abstractmethod
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply market type specific filters."""
        pass
    
    def apply_mae_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply MAE indicator filter if enabled."""
        if not self.config.mae_enabled:
            logger.debug("MAE indicator is disabled, skipping MAE filter")
            return market_data

        try:
            # Initialize MAE analyzer with config settings
            mae_analyzer = MAEAnalyzer(
                length=self.config.mae_length,
                source=self.config.mae_source,
                offset=self.config.mae_offset,
                smoothing_line=self.config.mae_smoothing_line,
                smoothing_length=self.config.mae_smoothing_length
            )

            filtered_data = {}
            mae_passed_count = 0

            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for MAE calculation
                    ohlc_data = self.fyers_client.get_ohlc_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) >= self.config.mae_length:
                        # Calculate MAE and check if price is passing through
                        use_smoothed = getattr(self.config, 'mae_smoothing_enabled', False)
                        is_passing = mae_analyzer.is_price_passing_through_mae(
                            ohlc_data,
                            current_price=data.ltp,
                            use_smoothed=use_smoothed
                        )

                        if is_passing:
                            # Calculate MAE value to attach to symbol
                            mae_default, mae_smoothed = mae_analyzer.calculate_mae(ohlc_data)
                            mae_series = mae_smoothed if use_smoothed else mae_default

                            if not mae_series.empty and not mae_series.isna().iloc[-1]:
                                mae_value = round(mae_series.iloc[-1], 2)
                                # Store MAE value in market data for later use
                                data.mae_value = mae_value
                                filtered_data[symbol] = data
                                mae_passed_count += 1
                                logger.debug(f"MAE filter passed for {symbol}: MAE={mae_value}")
                            else:
                                logger.debug(f"MAE filter failed for {symbol}: invalid MAE value")
                        else:
                            logger.debug(f"MAE filter failed for {symbol}: price not passing through MAE")
                    else:
                        logger.debug(f"MAE filter skipped for {symbol}: insufficient OHLC data")

                except Exception as e:
                    logger.warning(f"Error applying MAE filter to {symbol}: {e}")
                    # Include symbol without MAE filtering if there's an error
                    filtered_data[symbol] = data

            logger.info(f"MAE filter: {mae_passed_count}/{len(market_data)} symbols passed")
            return filtered_data

        except Exception as e:
            logger.error(f"Error in MAE filtering: {e}")
            # Return original data if MAE filtering fails
            return market_data

    def convert_to_filtered_symbols(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """Convert market data to FilteredSymbol objects."""
        filtered_symbols = []

        for symbol, data in market_data.items():
            # Parse symbol to get metadata
            csv_file = self.config.get_csv_file_for_market_type(self.market_type)
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)

            if parsed_symbol:
                filtered_symbol = FilteredSymbol(
                    symbol=symbol,
                    underlying=parsed_symbol.underlying,
                    market_type=self.market_type,
                    market_data=data,
                    suffix=parsed_symbol.suffix,
                    expiry_year=parsed_symbol.expiry_year,
                    expiry_month=parsed_symbol.expiry_month,
                    strike_price=parsed_symbol.strike_price,
                    option_type=parsed_symbol.option_type
                )

                # Add MAE value if available
                if hasattr(data, 'mae_value'):
                    filtered_symbol.mae_value = data.mae_value

                filtered_symbols.append(filtered_symbol)

        return filtered_symbols
    
    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")
            
            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []
            
            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)
            
            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []
            
            logger.info(f"Fetching market data for {len(symbols_to_scan)} {self.market_type} symbols")
            
            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            filtered_data = self.apply_mae_filter(filtered_data)
            
            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)
            
            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class EquityScanner(BaseMarketScanner):
    """Scanner for equity symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'EQUITY')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply equity-specific filters."""
        # No additional filters for equity beyond volume and LTP
        return market_data


class IndexScanner(BaseMarketScanner):
    """Scanner for index symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'INDEX')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply index-specific filters."""
        # No additional filters for index beyond volume and LTP
        return market_data


class FuturesScanner(BaseMarketScanner):
    """Scanner for futures symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'FUTURES')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply futures-specific filters."""
        # No additional filters for futures beyond volume and LTP
        return market_data


class OptionsScanner(BaseMarketScanner):
    """Scanner for options symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'OPTIONS')
        self.options_filter = OptionsChainFilter(config)

    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning with pre-filtering for options to reduce API calls."""
        try:
            # Get all options symbols first
            all_options_symbols = self.symbol_parser.get_symbols_for_market_type(
                self.market_type, underlying_symbols
            )

            logger.info(f"Loaded {len(all_options_symbols)} raw OPTIONS symbols")

            # Apply pre-filtering to reduce symbols before API calls
            if len(all_options_symbols) > 10000:  # Only pre-filter if we have too many symbols
                filtered_symbols = self._apply_pre_filtering(all_options_symbols, underlying_symbols)
                logger.info(f"Pre-filtering reduced symbols from {len(all_options_symbols)} to {len(filtered_symbols)}")
                return filtered_symbols
            else:
                return all_options_symbols

        except Exception as e:
            logger.error(f"Error in get_symbols_for_scanning: {e}")
            return []

    def _apply_pre_filtering(self, symbols: List[str], underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Apply pre-filtering to options symbols based on spot prices and strike ranges."""
        try:
            # Parse symbols to UniversalSymbol objects
            csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
            parsed_symbols = []

            for symbol in symbols:
                parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
                if parsed_symbol and parsed_symbol.is_options():
                    parsed_symbols.append(parsed_symbol)

            logger.info(f"Parsed {len(parsed_symbols)} valid options symbols for pre-filtering")

            # Get unique underlying symbols
            unique_underlyings = list(set(s.underlying for s in parsed_symbols))
            logger.info(f"Found {len(unique_underlyings)} unique underlying symbols")

            # Fetch spot prices for underlying symbols
            spot_prices = self.options_filter.get_spot_prices_for_underlyings(unique_underlyings)
            logger.info(f"Retrieved spot prices for {len(spot_prices)} underlying symbols")

            # Apply aggressive pre-filtering to reduce symbols significantly
            max_symbols_per_underlying = 150  # Limit symbols per underlying for API efficiency
            filtered_symbols = self.options_filter.pre_filter_options_symbols(
                parsed_symbols, spot_prices, max_symbols_per_underlying
            )

            # Convert back to NSE symbol strings
            filtered_symbol_strings = [s.get_nse_symbol() for s in filtered_symbols]

            logger.info(f"Pre-filtering completed: {len(filtered_symbol_strings)}/{len(symbols)} symbols selected")
            return filtered_symbol_strings

        except Exception as e:
            logger.error(f"Error in pre-filtering: {e}")
            # Return original symbols if pre-filtering fails
            return symbols

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply options-specific filters including CE/PE pairing."""
        # Since we already applied pre-filtering, we can apply lighter post-filtering here
        # Convert market data to UniversalSymbol objects for final options filtering
        symbols = []
        csv_file = self.config.get_csv_file_for_market_type('OPTIONS')

        for symbol in market_data.keys():
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
            if parsed_symbol:
                symbols.append(parsed_symbol)

        # Apply final options chain filtering (this should be much lighter now)
        filtered_symbols = self.options_filter.filter_options_symbols(symbols)

        # Convert back to market data dictionary
        filtered_data = {}
        filtered_symbol_names = {s.get_nse_symbol() for s in filtered_symbols}

        for symbol, data in market_data.items():
            if symbol in filtered_symbol_names:
                filtered_data[symbol] = data

        logger.info(f"Options chain filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data


class MarketTypeScannerFactory:
    """Factory for creating market type scanners."""
    
    @staticmethod
    def create_scanner(market_type: str, config: ConfigLoader) -> BaseMarketScanner:
        """
        Create a scanner for the specified market type.
        
        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
            config: Configuration loader instance
            
        Returns:
            Appropriate scanner instance
        """
        if market_type == 'EQUITY':
            return EquityScanner(config)
        elif market_type == 'INDEX':
            return IndexScanner(config)
        elif market_type == 'FUTURES':
            return FuturesScanner(config)
        elif market_type == 'OPTIONS':
            return OptionsScanner(config)
        else:
            raise ValueError(f"Unknown market type: {market_type}")
    
    @staticmethod
    def get_supported_market_types() -> List[str]:
        """Get list of supported market types."""
        return ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']

